ESX = exports["es_extended"]:getSharedObject()

-- Funkce pro generování čísla faktury
local function generateInvoiceNumber()
    local timestamp = os.time()
    local random = math.random(1000, 9999)
    return 'INV-' .. timestamp .. '-' .. random
end

-- Funkce pro kontrolu oprávnění
local function hasPermission(xPlayer, action)
    if action == 'admin' then
        for _, group in pairs(Config.AdminGroups) do
            if xPlayer.getGroup() == group then
                return true
            end
        end
        return false
    elseif action == 'create_invoice' then
        for _, job in pairs(Config.InvoiceJobs) do
            if xPlayer.job.name == job then
                return true
            end
        end
        return false
    end
    return false
end

-- Funkce pro odeslání Discord webhook
local function sendDiscordLog(title, description, color)
    if Config.DiscordWebhook == '' then return end
    
    local embed = {
        {
            ["title"] = title,
            ["description"] = description,
            ["type"] = "rich",
            ["color"] = color or 3447003,
            ["footer"] = {
                ["text"] = "Faktury System",
            },
            ["timestamp"] = os.date("!%Y-%m-%dT%H:%M:%SZ"),
        }
    }
    
    PerformHttpRequest(Config.DiscordWebhook, function(err, text, headers) end, 'POST', json.encode({
        username = Config.DiscordBotName,
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Event pro otevření UI (pouze admin)
RegisterNetEvent('pitrs_invoices:openUI')
AddEventHandler('pitrs_invoices:openUI', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    if not hasPermission(xPlayer, 'admin') then
        xPlayer.showNotification(Config.Notifications['no_permission'])
        return
    end
    
    TriggerClientEvent('pitrs_invoices:showUI', source)
end)

-- Event pro získání všech faktur (admin)
RegisterNetEvent('pitrs_invoices:getInvoices')
AddEventHandler('pitrs_invoices:getInvoices', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    if not hasPermission(xPlayer, 'admin') then
        return
    end
    
    MySQL.query('SELECT * FROM pitrs_invoices ORDER BY created_at DESC', {}, function(result)
        TriggerClientEvent('pitrs_invoices:receiveInvoices', source, result)
    end)
end)

-- Event pro získání faktur hráče
RegisterNetEvent('pitrs_invoices:getPlayerInvoices')
AddEventHandler('pitrs_invoices:getPlayerInvoices', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    MySQL.query('SELECT * FROM pitrs_invoices WHERE receiver_identifier = ? ORDER BY created_at DESC', {
        xPlayer.identifier
    }, function(result)
        TriggerClientEvent('pitrs_invoices:receivePlayerInvoices', source, result)
    end)
end)

-- Event pro vytvoření faktury
RegisterNetEvent('pitrs_invoices:createInvoice')
AddEventHandler('pitrs_invoices:createInvoice', function(targetId, amount, description)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    if not xPlayer or not xTarget then
        if xPlayer then
            xPlayer.showNotification(Config.Notifications['invalid_player'])
        end
        return
    end

    if not hasPermission(xPlayer, 'create_invoice') then
        xPlayer.showNotification(Config.Notifications['no_permission'])
        return
    end

    amount = tonumber(amount)
    if not amount or amount < Config.MinInvoiceAmount or amount > Config.MaxInvoiceAmount then
        xPlayer.showNotification(Config.Notifications['invalid_amount'])
        return
    end

    local invoiceNumber = generateInvoiceNumber()
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.InvoiceExpireDays * 24 * 60 * 60))

    MySQL.insert('INSERT INTO pitrs_invoices (invoice_number, sender_identifier, sender_name, sender_job, receiver_identifier, receiver_name, amount, description, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        invoiceNumber,
        xPlayer.identifier,
        xPlayer.getName(),
        xPlayer.job.name,
        xTarget.identifier,
        xTarget.getName(),
        amount,
        description,
        expiresAt
    }, function(insertId)
        if insertId then
            xPlayer.showNotification(Config.Notifications['invoice_sent'])
            xTarget.showNotification(Config.Notifications['invoice_received'])

            -- Discord log
            sendDiscordLog(
                'Nová faktura vystavena',
                string.format('**Vystavil:** %s (%s)\n**Příjemce:** %s\n**Částka:** %s %s\n**Popis:** %s\n**Číslo faktury:** %s',
                    xPlayer.getName(), xPlayer.job.label, xTarget.getName(), ESX.Math.GroupDigits(amount), Config.Currency, description, invoiceNumber),
                3447003
            )

            -- Aktualizace UI
            TriggerClientEvent('pitrs_invoices:invoiceCreated', -1)
        end
    end)
end)

-- Event pro zaplacení faktury
RegisterNetEvent('pitrs_invoices:payInvoice')
AddEventHandler('pitrs_invoices:payInvoice', function(invoiceId)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    MySQL.single('SELECT * FROM pitrs_invoices WHERE id = ? AND receiver_identifier = ? AND status = "pending"', {
        invoiceId, xPlayer.identifier
    }, function(invoice)
        if not invoice then
            xPlayer.showNotification(Config.Notifications['invoice_not_found'])
            return
        end

        -- Kontrola vypršení
        local expiresAt = os.time({
            year = tonumber(string.sub(invoice.expires_at, 1, 4)),
            month = tonumber(string.sub(invoice.expires_at, 6, 7)),
            day = tonumber(string.sub(invoice.expires_at, 9, 10)),
            hour = tonumber(string.sub(invoice.expires_at, 12, 13)),
            min = tonumber(string.sub(invoice.expires_at, 15, 16)),
            sec = tonumber(string.sub(invoice.expires_at, 18, 19))
        })

        if os.time() > expiresAt then
            MySQL.update('UPDATE pitrs_invoices SET status = "expired" WHERE id = ?', {invoiceId})
            xPlayer.showNotification(Config.Notifications['invoice_expired'])
            return
        end

        -- Kontrola peněz
        if xPlayer.getMoney() < invoice.amount then
            xPlayer.showNotification(Config.Notifications['insufficient_funds'])
            return
        end

        -- Zaplacení faktury
        xPlayer.removeMoney(invoice.amount)

        -- Najít odesílatele a přidat mu peníze
        local xSender = ESX.GetPlayerFromIdentifier(invoice.sender_identifier)
        if xSender then
            xSender.addMoney(invoice.amount)
        else
            -- Pokud odesílatel není online, přidat peníze do databáze
            MySQL.update('UPDATE users SET money = money + ? WHERE identifier = ?', {
                invoice.amount, invoice.sender_identifier
            })
        end

        -- Aktualizovat fakturu
        MySQL.update('UPDATE pitrs_invoices SET status = "paid", paid_at = NOW() WHERE id = ?', {invoiceId})

        xPlayer.showNotification(Config.Notifications['invoice_paid'])

        -- Discord log
        sendDiscordLog(
            'Faktura zaplacena',
            string.format('**Zaplatil:** %s\n**Částka:** %s %s\n**Číslo faktury:** %s',
                xPlayer.getName(), ESX.Math.GroupDigits(invoice.amount), Config.Currency, invoice.invoice_number),
            65280
        )

        -- Aktualizace UI
        TriggerClientEvent('pitrs_invoices:invoicePaid', -1, invoiceId)
    end)
end)

-- Event pro zrušení faktury (admin)
RegisterNetEvent('pitrs_invoices:cancelInvoice')
AddEventHandler('pitrs_invoices:cancelInvoice', function(invoiceId)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasPermission(xPlayer, 'admin') then
        xPlayer.showNotification(Config.Notifications['no_permission'])
        return
    end

    MySQL.update('UPDATE pitrs_invoices SET status = "cancelled" WHERE id = ?', {invoiceId}, function(affectedRows)
        if affectedRows > 0 then
            xPlayer.showNotification('Faktura byla zrušena!')
            TriggerClientEvent('pitrs_invoices:invoiceCancelled', -1, invoiceId)
        end
    end)
end)

-- Event pro získání online hráčů
RegisterNetEvent('pitrs_invoices:getOnlinePlayers')
AddEventHandler('pitrs_invoices:getOnlinePlayers', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasPermission(xPlayer, 'create_invoice') then
        return
    end

    local players = {}
    local xPlayers = ESX.GetExtendedPlayers()

    for _, player in pairs(xPlayers) do
        if player.source ~= source then
            table.insert(players, {
                id = player.source,
                name = player.getName(),
                identifier = player.identifier
            })
        end
    end

    TriggerClientEvent('pitrs_invoices:receiveOnlinePlayers', source, players)
end)

-- Automatické označení vypršených faktur
CreateThread(function()
    while true do
        Wait(60000) -- Kontrola každou minutu

        MySQL.update('UPDATE pitrs_invoices SET status = "expired" WHERE status = "pending" AND expires_at < NOW()', {})

        Wait(3600000) -- Čekání hodinu před další kontrolou
    end
end)

-- Export funkce pro jiné skripty
exports('createInvoice', function(senderIdentifier, receiverIdentifier, amount, description)
    local invoiceNumber = generateInvoiceNumber()
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.InvoiceExpireDays * 24 * 60 * 60))

    MySQL.insert('INSERT INTO pitrs_invoices (invoice_number, sender_identifier, sender_name, sender_job, receiver_identifier, receiver_name, amount, description, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        invoiceNumber,
        senderIdentifier,
        'System',
        'system',
        receiverIdentifier,
        'Player',
        amount,
        description,
        expiresAt
    })

    return invoiceNumber
end)
