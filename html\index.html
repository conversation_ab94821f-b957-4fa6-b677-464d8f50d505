<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faktury System</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app" v-show="showUI" class="invoice-container">
        <div class="invoice-modal">
            <!-- Header -->
            <div class="modal-header">
                <div class="header-left">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <h2>Správa Faktur</h2>
                </div>
                <button @click="closeUI" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button @click="activeTab = 'invoices'" :class="{ active: activeTab === 'invoices' }">
                    <i class="fas fa-list"></i>
                    Všechny Faktury
                </button>
                <button @click="activeTab = 'create'" :class="{ active: activeTab === 'create' }">
                    <i class="fas fa-plus"></i>
                    Vytvořit Fakturu
                </button>
                <button @click="activeTab = 'my-invoices'" :class="{ active: activeTab === 'my-invoices' }">
                    <i class="fas fa-user"></i>
                    Moje Faktury
                </button>
            </div>

            <!-- Content -->
            <div class="modal-content">
                <!-- Všechny faktury -->
                <div v-if="activeTab === 'invoices'" class="tab-content">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input v-model="searchTerm" type="text" placeholder="Hledat faktury...">
                    </div>
                    
                    <div class="invoices-grid">
                        <div v-for="invoice in filteredInvoices" :key="invoice.id" class="invoice-card" :class="'status-' + invoice.status">
                            <div class="invoice-header">
                                <span class="invoice-number">{{ invoice.invoice_number }}</span>
                                <span class="invoice-status" :class="'status-' + invoice.status">
                                    {{ getStatusText(invoice.status) }}
                                </span>
                            </div>
                            <div class="invoice-details">
                                <div class="detail-row">
                                    <span class="label">Od:</span>
                                    <span class="value">{{ invoice.sender_name }} ({{ invoice.sender_job }})</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Pro:</span>
                                    <span class="value">{{ invoice.receiver_name }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Částka:</span>
                                    <span class="value amount">${{ formatAmount(invoice.amount) }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Popis:</span>
                                    <span class="value">{{ invoice.description }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Vytvořeno:</span>
                                    <span class="value">{{ formatDate(invoice.created_at) }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Vyprší:</span>
                                    <span class="value">{{ formatDate(invoice.expires_at) }}</span>
                                </div>
                            </div>
                            <div class="invoice-actions" v-if="invoice.status === 'pending'">
                                <button @click="cancelInvoice(invoice.id)" class="btn btn-danger">
                                    <i class="fas fa-times"></i>
                                    Zrušit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vytvoření faktury -->
                <div v-if="activeTab === 'create'" class="tab-content">
                    <div class="create-form">
                        <h3><i class="fas fa-plus-circle"></i> Vytvořit novou fakturu</h3>
                        
                        <div class="form-group">
                            <label>Příjemce</label>
                            <select v-model="newInvoice.targetId" required>
                                <option value="">Vyberte hráče...</option>
                                <option v-for="player in players" :key="player.id" :value="player.id">
                                    {{ player.name }} (ID: {{ player.id }})
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Částka ($)</label>
                            <input v-model="newInvoice.amount" type="number" min="1" max="1000000" placeholder="Zadejte částku..." required>
                        </div>

                        <div class="form-group">
                            <label>Popis</label>
                            <textarea v-model="newInvoice.description" placeholder="Popis faktury..." rows="4" required></textarea>
                        </div>

                        <div class="form-actions">
                            <button @click="createInvoice" class="btn btn-primary" :disabled="!canCreateInvoice">
                                <i class="fas fa-paper-plane"></i>
                                Vystavit Fakturu
                            </button>
                            <button @click="resetForm" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                Resetovat
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Moje faktury -->
                <div v-if="activeTab === 'my-invoices'" class="tab-content">
                    <div class="my-invoices-header">
                        <h3><i class="fas fa-user-circle"></i> Moje faktury</h3>
                        <button @click="loadMyInvoices" class="btn btn-primary">
                            <i class="fas fa-refresh"></i>
                            Obnovit
                        </button>
                    </div>
                    
                    <div class="invoices-grid">
                        <div v-for="invoice in myInvoices" :key="invoice.id" class="invoice-card" :class="'status-' + invoice.status">
                            <div class="invoice-header">
                                <span class="invoice-number">{{ invoice.invoice_number }}</span>
                                <span class="invoice-status" :class="'status-' + invoice.status">
                                    {{ getStatusText(invoice.status) }}
                                </span>
                            </div>
                            <div class="invoice-details">
                                <div class="detail-row">
                                    <span class="label">Od:</span>
                                    <span class="value">{{ invoice.sender_name }} ({{ invoice.sender_job }})</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Částka:</span>
                                    <span class="value amount">${{ formatAmount(invoice.amount) }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Popis:</span>
                                    <span class="value">{{ invoice.description }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Vytvořeno:</span>
                                    <span class="value">{{ formatDate(invoice.created_at) }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Vyprší:</span>
                                    <span class="value">{{ formatDate(invoice.expires_at) }}</span>
                                </div>
                            </div>
                            <div class="invoice-actions" v-if="invoice.status === 'pending'">
                                <button @click="payInvoice(invoice.id)" class="btn btn-success">
                                    <i class="fas fa-credit-card"></i>
                                    Zaplatit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="vue.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
