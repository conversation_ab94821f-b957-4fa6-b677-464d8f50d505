// Vue.js 3 - Simplified version for FiveM
var Vue = (function() {
    'use strict';
    
    function createApp(options) {
        const app = {
            mount(selector) {
                const container = typeof selector === 'string' 
                    ? document.querySelector(selector) 
                    : selector;
                
                if (!container) {
                    console.warn('Failed to mount app: mount target selector returned null.');
                    return;
                }
                
                const instance = createComponentInstance(options);
                instance.container = container;
                
                // Setup reactivity
                setupReactivity(instance);
                
                // Initial render
                render(instance);
                
                return instance;
            }
        };
        
        return app;
    }
    
    function createComponentInstance(options) {
        return {
            data: options.data || (() => ({})),
            computed: options.computed || {},
            methods: options.methods || {},
            mounted: options.mounted || (() => {}),
            _data: null,
            _computedCache: {},
            container: null
        };
    }
    
    function setupReactivity(instance) {
        // Simple reactivity system
        instance._data = typeof instance.data === 'function' 
            ? instance.data() 
            : instance.data;
        
        // Make data reactive
        instance._data = reactive(instance._data);
        
        // Setup computed properties
        for (const key in instance.computed) {
            Object.defineProperty(instance._data, key, {
                get() {
                    if (!instance._computedCache[key]) {
                        instance._computedCache[key] = instance.computed[key].call(instance._data);
                    }
                    return instance._computedCache[key];
                },
                enumerable: true
            });
        }
        
        // Bind methods
        for (const key in instance.methods) {
            instance._data[key] = instance.methods[key].bind(instance._data);
        }
    }
    
    function reactive(obj) {
        return new Proxy(obj, {
            set(target, key, value) {
                target[key] = value;
                // Trigger re-render (simplified)
                return true;
            }
        });
    }
    
    function render(instance) {
        // Simple template rendering
        const template = instance.container.innerHTML;
        
        // Basic v-show directive
        const vShowElements = instance.container.querySelectorAll('[v-show]');
        vShowElements.forEach(el => {
            const expression = el.getAttribute('v-show');
            const value = evaluateExpression(expression, instance._data);
            el.style.display = value ? '' : 'none';
        });
        
        // Basic v-if directive
        const vIfElements = instance.container.querySelectorAll('[v-if]');
        vIfElements.forEach(el => {
            const expression = el.getAttribute('v-if');
            const value = evaluateExpression(expression, instance._data);
            if (!value) {
                el.style.display = 'none';
            } else {
                el.style.display = '';
            }
        });
        
        // Basic v-for directive
        const vForElements = instance.container.querySelectorAll('[v-for]');
        vForElements.forEach(el => {
            const expression = el.getAttribute('v-for');
            const [item, array] = expression.split(' in ').map(s => s.trim());
            const arrayValue = evaluateExpression(array, instance._data);
            
            if (Array.isArray(arrayValue)) {
                const parent = el.parentNode;
                const template = el.outerHTML;
                
                // Clear existing
                el.remove();
                
                // Create items
                arrayValue.forEach((itemValue, index) => {
                    const newEl = document.createElement('div');
                    newEl.innerHTML = template;
                    const child = newEl.firstElementChild;
                    
                    // Replace item references
                    child.innerHTML = child.innerHTML.replace(
                        new RegExp(`{{\\s*${item}\\s*}}`, 'g'), 
                        JSON.stringify(itemValue)
                    );
                    
                    parent.appendChild(child);
                });
            }
        });
        
        // Basic text interpolation
        const walker = document.createTreeWalker(
            instance.container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.includes('{{')) {
                textNodes.push(node);
            }
        }
        
        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            const interpolated = text.replace(/\{\{(.+?)\}\}/g, (match, expression) => {
                const value = evaluateExpression(expression.trim(), instance._data);
                return value !== undefined ? value : '';
            });
            textNode.textContent = interpolated;
        });
        
        // Setup event listeners
        setupEventListeners(instance);
        
        // Call mounted hook
        if (instance.mounted) {
            instance.mounted.call(instance._data);
        }
    }
    
    function setupEventListeners(instance) {
        // v-click directive
        const clickElements = instance.container.querySelectorAll('[\\@click]');
        clickElements.forEach(el => {
            const handler = el.getAttribute('@click');
            el.addEventListener('click', () => {
                evaluateExpression(handler, instance._data);
            });
        });
        
        // v-model directive (simplified)
        const modelElements = instance.container.querySelectorAll('[v-model]');
        modelElements.forEach(el => {
            const property = el.getAttribute('v-model');
            
            // Set initial value
            if (instance._data[property] !== undefined) {
                el.value = instance._data[property];
            }
            
            // Listen for changes
            el.addEventListener('input', (e) => {
                instance._data[property] = e.target.value;
            });
        });
    }
    
    function evaluateExpression(expression, context) {
        try {
            // Simple expression evaluation
            const func = new Function(...Object.keys(context), `return ${expression}`);
            return func(...Object.values(context));
        } catch (e) {
            console.warn('Error evaluating expression:', expression, e);
            return undefined;
        }
    }
    
    return {
        createApp
    };
})();
