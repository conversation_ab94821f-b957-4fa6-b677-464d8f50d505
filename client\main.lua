ESX = exports["es_extended"]:getSharedObject()

local isUIOpen = false
local invoicesData = {}
local onlinePlayers = {}
local shouldOpenUI = false -- Flag pro kontrolu, zda se má UI otevřít

-- Funkce pro otevření UI
local function openUI()
    if isUIOpen then return end
    
    isUIOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'showUI',
        invoices = invoicesData,
        players = onlinePlayers
    })
end

-- Funkce pro zavření UI
local function closeUI()
    if not isUIOpen then return end
    
    isUIOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'hideUI'
    })
end

-- Klávesová zkratka pro otevření UI (admin)
RegisterKeyMapping('openinvoices', 'Otevřít faktury (Admin)', 'keyboard', Config.UIKey)

RegisterCommand('openinvoices', function()
    TriggerServerEvent('pitrs_invoices:openUI')
end, false)

-- Event pro zobrazení UI
RegisterNetEvent('pitrs_invoices:showUI')
AddEventHandler('pitrs_invoices:showUI', function()
    -- Nastavíme flag, že se má UI otevřít
    shouldOpenUI = true
    -- Načíst data před otevřením UI
    TriggerServerEvent('pitrs_invoices:getInvoices')
    TriggerServerEvent('pitrs_invoices:getOnlinePlayers')
end)

-- Event pro přijetí faktur
RegisterNetEvent('pitrs_invoices:receiveInvoices')
AddEventHandler('pitrs_invoices:receiveInvoices', function(invoices)
    invoicesData = invoices
    if isUIOpen then
        SendNUIMessage({
            action = 'updateInvoices',
            invoices = invoices
        })
    elseif shouldOpenUI then
        -- Otevřeme UI pouze pokud byla explicitně požádána o otevření
        shouldOpenUI = false -- Resetujeme flag
        openUI()
    end
end)

-- Event pro přijetí online hráčů
RegisterNetEvent('pitrs_invoices:receiveOnlinePlayers')
AddEventHandler('pitrs_invoices:receiveOnlinePlayers', function(players)
    onlinePlayers = players
    if isUIOpen then
        SendNUIMessage({
            action = 'updatePlayers',
            players = players
        })
    end
end)

-- Event pro přijetí faktur hráče
RegisterNetEvent('pitrs_invoices:receivePlayerInvoices')
AddEventHandler('pitrs_invoices:receivePlayerInvoices', function(invoices)
    SendNUIMessage({
        action = 'showPlayerInvoices',
        invoices = invoices
    })
end)

-- Event pro aktualizaci po vytvoření faktury
RegisterNetEvent('pitrs_invoices:invoiceCreated')
AddEventHandler('pitrs_invoices:invoiceCreated', function()
    if isUIOpen then
        TriggerServerEvent('pitrs_invoices:getInvoices')
    end
end)

-- Event pro aktualizaci po zaplacení faktury
RegisterNetEvent('pitrs_invoices:invoicePaid')
AddEventHandler('pitrs_invoices:invoicePaid', function(invoiceId)
    if isUIOpen then
        TriggerServerEvent('pitrs_invoices:getInvoices')
    end
end)

-- Event pro aktualizaci po zrušení faktury
RegisterNetEvent('pitrs_invoices:invoiceCancelled')
AddEventHandler('pitrs_invoices:invoiceCancelled', function(invoiceId)
    if isUIOpen then
        TriggerServerEvent('pitrs_invoices:getInvoices')
    end
end)

-- NUI Callbacks
RegisterNUICallback('closeUI', function(data, cb)
    closeUI()
    cb('ok')
end)

RegisterNUICallback('createInvoice', function(data, cb)
    TriggerServerEvent('pitrs_invoices:createInvoice', data.targetId, data.amount, data.description)
    cb('ok')
end)

RegisterNUICallback('payInvoice', function(data, cb)
    TriggerServerEvent('pitrs_invoices:payInvoice', data.invoiceId)
    cb('ok')
end)

RegisterNUICallback('cancelInvoice', function(data, cb)
    TriggerServerEvent('pitrs_invoices:cancelInvoice', data.invoiceId)
    cb('ok')
end)

RegisterNUICallback('getPlayerInvoices', function(data, cb)
    TriggerServerEvent('pitrs_invoices:getPlayerInvoices')
    cb('ok')
end)

-- Příkaz pro zobrazení vlastních faktur
RegisterCommand('faktury', function()
    shouldOpenUI = true -- Nastavíme flag pro otevření UI
    TriggerServerEvent('pitrs_invoices:getPlayerInvoices')
end, false)

-- Příkaz pro zaplacení faktury podle ID
RegisterCommand('zaplatfakturu', function(source, args)
    if not args[1] then
        lib.notify({
            type = 'error',
            description = 'Použití: /zaplatfakturu [ID faktury]'
        })
        return
    end

    local invoiceId = tonumber(args[1])
    if not invoiceId then
        lib.notify({
            type = 'error',
            description = 'Neplatné ID faktury!'
        })
        return
    end

    TriggerServerEvent('pitrs_invoices:payInvoice', invoiceId)
end, false)

-- Zavření UI při smrti nebo odpojení
AddEventHandler('esx:onPlayerDeath', function()
    if isUIOpen then
        closeUI()
    end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName and isUIOpen then
        closeUI()
    end
end)
