const { createApp } = Vue;

createApp({
    data() {
        return {
            showUI: false,
            activeTab: 'invoices',
            searchTerm: '',
            invoices: [],
            myInvoices: [],
            players: [],
            newInvoice: {
                targetId: '',
                amount: '',
                description: ''
            },
            loading: false
        }
    },
    computed: {
        filteredInvoices() {
            if (!this.searchTerm) return this.invoices;
            
            const term = this.searchTerm.toLowerCase();
            return this.invoices.filter(invoice => 
                invoice.invoice_number.toLowerCase().includes(term) ||
                invoice.sender_name.toLowerCase().includes(term) ||
                invoice.receiver_name.toLowerCase().includes(term) ||
                invoice.description.toLowerCase().includes(term)
            );
        },
        canCreateInvoice() {
            return this.newInvoice.targetId && 
                   this.newInvoice.amount && 
                   this.newInvoice.description &&
                   this.newInvoice.amount > 0 &&
                   this.newInvoice.amount <= 1000000;
        }
    },
    methods: {
        closeUI() {
            this.showUI = false;
            fetch(`https://${GetParentResourceName()}/closeUI`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });
        },
        
        createInvoice() {
            if (!this.canCreateInvoice) return;
            
            fetch(`https://${GetParentResourceName()}/createInvoice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    targetId: parseInt(this.newInvoice.targetId),
                    amount: parseInt(this.newInvoice.amount),
                    description: this.newInvoice.description
                })
            });
            
            this.resetForm();
            this.showNotification('Faktura byla odeslána!', 'success');
        },
        
        payInvoice(invoiceId) {
            fetch(`https://${GetParentResourceName()}/payInvoice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    invoiceId: invoiceId
                })
            });
            
            this.showNotification('Platba byla odeslána!', 'info');
        },
        
        cancelInvoice(invoiceId) {
            if (!confirm('Opravdu chcete zrušit tuto fakturu?')) return;
            
            fetch(`https://${GetParentResourceName()}/cancelInvoice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    invoiceId: invoiceId
                })
            });
            
            this.showNotification('Faktura byla zrušena!', 'warning');
        },
        
        loadMyInvoices() {
            fetch(`https://${GetParentResourceName()}/getPlayerInvoices`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });
        },
        
        resetForm() {
            this.newInvoice = {
                targetId: '',
                amount: '',
                description: ''
            };
        },
        
        formatAmount(amount) {
            return new Intl.NumberFormat('cs-CZ').format(amount);
        },
        
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('cs-CZ', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        },
        
        getStatusText(status) {
            const statusTexts = {
                'pending': 'Čeká na zaplacení',
                'paid': 'Zaplaceno',
                'expired': 'Vypršelo',
                'cancelled': 'Zrušeno'
            };
            return statusTexts[status] || status;
        },
        
        showNotification(message, type = 'info') {
            // Vytvoření notifikace
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Automatické odstranění po 3 sekundách
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        },
        
        getNotificationIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
    },
    
    mounted() {
        // Poslouchání zpráv z FiveM
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            switch (data.action) {
                case 'showUI':
                    this.showUI = true;
                    this.invoices = data.invoices || [];
                    this.players = data.players || [];
                    break;
                    
                case 'hideUI':
                    this.showUI = false;
                    break;
                    
                case 'updateInvoices':
                    this.invoices = data.invoices || [];
                    break;
                    
                case 'updatePlayers':
                    this.players = data.players || [];
                    break;
                    
                case 'showPlayerInvoices':
                    this.myInvoices = data.invoices || [];
                    this.activeTab = 'my-invoices';
                    if (!this.showUI) {
                        this.showUI = true;
                    }
                    break;
            }
        });
        
        // Zavření UI při stisknutí ESC
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.showUI) {
                this.closeUI();
            }
        });
    }
}).mount('#app');
