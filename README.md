# 🧾 Pitrs Faktury - ESX Legacy Faktury Systém

Moderní a uživatelsky přívětivý systém pro správu faktur v ESX Legacy frameworku s krásným Vue.js UI.

## ✨ Funkce

- 🎨 **Moderní UI** - Responzivní design s Vue.js
- 💰 **Správa faktur** - Vytváření, zobrazování a placení faktur
- 👥 **Multi-job podpora** - Různ<PERSON> joby mohou vystavovat faktury
- 🔒 **Admin panel** - Kompletní správa všech faktur
- 📱 **Responzivní design** - Funguje na všech zařízeních
- 🔔 **Notifikace** - Discord webhook podpora
- ⚡ **Optimalizované** - Rychlé a efektivní

## 📋 Požadavky

- ESX Legacy
- oxmysql
- Moderní webový prohlížeč

## 🚀 Instalace

1. **Stáhněte a umístěte** skript do složky `resources/[custom]/pitrs_faktury`

2. **Importujte databázi** - Spusťte SQL soubor `pitrs_invoices.sql` ve vaší databázi

3. **Přidejte do server.cfg**:
   ```
   ensure pitrs_faktury
   ```

4. **Restartujte server**

## ⚙️ Konfigurace

Upravte soubor `config.lua` podle vašich potřeb:

```lua
-- Základní nastavení
Config.Currency = 'Kč'
Config.MaxInvoiceAmount = 1000000
Config.InvoiceExpireDays = 30

-- Klávesa pro admin UI
Config.UIKey = 'F7'

-- Joby které mohou vystavovat faktury
Config.InvoiceJobs = {
    'police',
    'ambulance',
    'mechanic',
    -- přidejte další joby...
}

-- Discord webhook (volitelné)
Config.DiscordWebhook = 'your_webhook_url_here'
```

## 🎮 Použití

### Pro hráče:
- `/faktury` - Zobrazí vaše faktury
- `/zaplatfakturu [ID]` - Zaplatí fakturu podle ID

### Pro administrátory:
- `F7` (nebo nastavená klávesa) - Otevře admin panel
- Kompletní správa všech faktur
- Možnost rušení faktur

### Pro joby s oprávněním:
- Možnost vystavovat faktury přes admin panel
- Automatické přidání peněz po zaplacení

## 🎨 UI Funkce

### Admin Panel:
- **Všechny faktury** - Přehled všech faktur v systému
- **Vytvořit fakturu** - Formulář pro nové faktury
- **Moje faktury** - Osobní faktury (pro hráče)

### Funkce:
- 🔍 **Vyhledávání** - Rychlé hledání faktur
- 📊 **Filtry** - Podle stavu, data, částky
- 💳 **Jednoduché placení** - Jedno kliknutí
- 🗑️ **Správa** - Rušení a úpravy

## 🛠️ API

### Export funkce:
```lua
-- Vytvoření faktury z jiného skriptu
exports.pitrs_faktury:createInvoice(senderIdentifier, receiverIdentifier, amount, description)
```

### Server eventy:
```lua
-- Vytvoření faktury
TriggerServerEvent('pitrs_invoices:createInvoice', targetId, amount, description)

-- Zaplacení faktury
TriggerServerEvent('pitrs_invoices:payInvoice', invoiceId)

-- Získání faktur hráče
TriggerServerEvent('pitrs_invoices:getPlayerInvoices')
```

## 📊 Databáze

Systém vytvoří tabulku `pitrs_invoices` s následující strukturou:
- `id` - Unikátní ID faktury
- `invoice_number` - Číslo faktury
- `sender_*` - Informace o odesílateli
- `receiver_*` - Informace o příjemci
- `amount` - Částka
- `description` - Popis
- `status` - Stav (pending/paid/expired/cancelled)
- `created_at` - Datum vytvoření
- `expires_at` - Datum vypršení

## 🎯 Stavy faktur

- **Pending** (Čeká na zaplacení) - Nová faktura
- **Paid** (Zaplaceno) - Faktura byla zaplacena
- **Expired** (Vypršelo) - Faktura vypršela
- **Cancelled** (Zrušeno) - Faktura byla zrušena

## 🔧 Troubleshooting

### Časté problémy:

1. **UI se neotevírá**
   - Zkontrolujte konzoli pro chyby
   - Ověřte, že máte admin oprávnění

2. **Faktury se neukládají**
   - Zkontrolujte databázové připojení
   - Ověřte, že je tabulka správně vytvořena

3. **Platby nefungují**
   - Zkontrolujte, že hráč má dostatek peněz
   - Ověřte, že faktura není vypršelá

## 📝 Changelog

### v1.0.0
- ✅ Základní systém faktur
- ✅ Moderní Vue.js UI
- ✅ Admin panel
- ✅ Discord webhook podpora
- ✅ Multi-job podpora

## 👨‍💻 Autor

**Pitrs** - ESX Legacy Developer

## 📄 Licence

Tento projekt je licencován pod MIT licencí.

## 🤝 Podpora

Pokud máte problémy nebo návrhy, kontaktujte autora nebo vytvořte issue.

---

**Užijte si moderní správu faktur! 🎉**
