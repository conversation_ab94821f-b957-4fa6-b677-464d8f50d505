CREATE TABLE IF NOT EXISTS `pitrs_invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) NOT NULL,
  `sender_identifier` varchar(50) NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `sender_job` varchar(50) NOT NULL,
  `receiver_identifier` varchar(50) NOT NULL,
  `receiver_name` varchar(100) NOT NULL,
  `amount` int(11) NOT NULL,
  `description` text NOT NULL,
  `status` enum('pending','paid','expired','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `paid_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `sender_identifier` (`sender_identifier`),
  KEY `receiver_identifier` (`receiver_identifier`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexy pro lepší výkon
CREATE INDEX idx_invoices_sender ON pitrs_invoices(sender_identifier, status);
CREATE INDEX idx_invoices_receiver ON pitrs_invoices(receiver_identifier, status);
CREATE INDEX idx_invoices_created ON pitrs_invoices(created_at);
CREATE INDEX idx_invoices_expires ON pitrs_invoices(expires_at);
